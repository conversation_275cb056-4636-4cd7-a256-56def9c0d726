---
interface Props {
  title: string;
  description: string;
  tags: string[];
  slug?: string;
  image?: string;
  heroImage?: string;
  index?: number;
  projectType?: string;
  featured?: boolean;
  status?: 'completed' | 'in-progress' | 'archived';
}

const {
  title,
  description,
  tags,
  slug,
  image,
  heroImage,
  index = 0,
  projectType,
  featured = false,
  status = 'completed'
} = Astro.props;
---

<article class={`group relative bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-3xl shadow-lg border border-slate-200/50 dark:border-slate-700/50 overflow-hidden hover:shadow-2xl hover:scale-105 transition-all duration-500 hover:bg-white dark:hover:bg-slate-700 min-h-96 flex flex-col ${featured ? 'ring-2 ring-blue-500/20' : ''}`} role="listitem">
  <!-- Hero Image -->
  {heroImage && (
    <div class="relative h-48 overflow-hidden">
      <img
        src={heroImage}
        alt={`${title} preview`}
        class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
        loading="lazy"
      />
      <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
    </div>
  )}

  <!-- Gradient overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-indigo-500/5 dark:from-blue-400/10 dark:via-transparent dark:to-indigo-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

  <!-- Badges Container -->
  <div class="absolute top-6 left-6 z-10 flex flex-col gap-2">
    <!-- Project number badge -->
    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:scale-110 transition-transform duration-300">
      {(index + 1).toString().padStart(2, '0')}
    </div>

    <!-- Featured badge -->
    {featured && (
      <div class="px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-bold rounded-full shadow-lg">
        Featured
      </div>
    )}

    <!-- Status badge -->
    {status !== 'completed' && (
      <div class={`px-3 py-1 text-white text-xs font-bold rounded-full shadow-lg ${
        status === 'in-progress' ? 'bg-gradient-to-r from-green-400 to-emerald-400' :
        'bg-gradient-to-r from-gray-400 to-slate-400'
      }`}>
        {status === 'in-progress' ? 'In Progress' : 'Archived'}
      </div>
    )}
  </div>

  <!-- Project Type Badge -->
  {projectType && (
    <div class="absolute top-6 right-6 z-10">
      <div class="px-3 py-1 bg-black/70 text-white text-xs font-medium rounded-full backdrop-blur-sm">
        {projectType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </div>
    </div>
  )}

  <!-- Action indicator -->
  <div class="absolute top-6 right-6 z-10 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
    <div class="w-10 h-10 bg-white/90 dark:bg-slate-700/90 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg">
      <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
      </svg>
    </div>
  </div>

  <!-- Content -->
  <div class="relative z-10 p-8 flex flex-col flex-grow">
    <!-- Header spacing for badges (adjust based on hero image) -->
    <div class={heroImage ? "h-2 mb-4" : "h-6 mb-4"}></div>

    <div class="flex-grow">
      <h3 class="text-2xl font-bold text-slate-800 dark:text-slate-100 mb-4 font-heading group-hover:text-blue-700 dark:group-hover:text-blue-400 transition-colors duration-300">
        {title}
      </h3>
      <p class="text-slate-600 dark:text-slate-300 leading-relaxed mb-6 line-clamp-3">
        {description}
      </p>
    </div>

    <!-- Enhanced tags -->
    <div class="flex flex-wrap gap-2 mt-auto">
      {tags.slice(0, 3).map((tag, tagIndex) => (
        <span
          class="px-4 py-2 bg-gradient-to-r from-slate-100 to-blue-50 dark:from-slate-700 dark:to-slate-600 text-slate-700 dark:text-slate-200 text-sm font-semibold rounded-xl border border-slate-200/50 dark:border-slate-600/50 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/50 dark:hover:to-indigo-900/50 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-300 hover:scale-105 hover:shadow-md"
          style={`animation-delay: ${tagIndex * 100}ms`}
        >
          {tag}
        </span>
      ))}
      {tags.length > 3 && (
        <span class="px-4 py-2 bg-gradient-to-r from-slate-200 to-slate-100 dark:from-slate-600 dark:to-slate-700 text-slate-600 dark:text-slate-300 text-sm font-semibold rounded-xl border border-slate-200/50 dark:border-slate-600/50">
          +{tags.length - 3}
        </span>
      )}
    </div>

    <!-- Hover effect line -->
    <div class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
  </div>
</article>