---
import Layout from '../layouts/Layout.astro';
import ProjectCard from '../components/ProjectCard.astro';
import { getEntry, getCollection } from 'astro:content';

const homepageContent = await getEntry('homepage', 'main');
const { hero, about } = homepageContent.data;

// Get portfolio projects for featured section
const portfolioProjects = await getCollection('portfolio');
const featuredProjects = portfolioProjects
  .sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime())
  .slice(0, 3); // Show latest 3 projects
---

<Layout title="Nob Hokleng | Enterprise Architect | Performance & Scalability" description="Helping startups and enterprises design backends for thousands of users.">
  <!-- Hero Section -->
  <section class="hero min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 relative overflow-hidden transition-colors duration-500" role="banner">
    <!-- Enhanced Animated background elements -->
    <div class="absolute inset-0">
      <!-- Floating geometric shapes -->
      <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-float"></div>
      <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-orange-400/10 to-pink-400/10 rounded-full blur-3xl animate-float-delayed"></div>
      <div class="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-r from-emerald-400/8 to-cyan-400/8 rounded-full blur-2xl animate-float-slow"></div>

      <!-- Floating particles -->
      <div class="absolute top-20 left-20 w-4 h-4 bg-blue-400/20 rounded-full animate-bounce-slow"></div>
      <div class="absolute top-40 right-32 w-3 h-3 bg-purple-400/20 rounded-full animate-bounce-delayed"></div>
      <div class="absolute bottom-32 left-1/3 w-2 h-2 bg-orange-400/20 rounded-full animate-bounce-slow"></div>
      <div class="absolute bottom-20 right-20 w-5 h-5 bg-emerald-400/20 rounded-full animate-bounce"></div>
    </div>
    
    <div class="container mx-auto px-6 max-w-7xl relative z-10">
      <div class="grid lg:grid-cols-2 gap-16 items-center">
        <!-- Content Side -->
        <div class="space-y-8 animate-fade-in-up">
          <div class="space-y-8">
            <!-- Enhanced status badge -->
            <div class="inline-flex items-center px-6 py-3 bg-white/90 dark:bg-slate-800/90 backdrop-blur-md rounded-full border border-blue-200/50 dark:border-slate-600/50 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 group">
              <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full mr-3 animate-pulse shadow-lg"></div>
              <span class="text-sm font-semibold text-slate-700 dark:text-slate-200 group-hover:text-blue-700 dark:group-hover:text-blue-400 transition-colors">Available for new opportunities</span>
              <div class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
              </div>
            </div>

            <!-- Enhanced headline with better typography -->
            <div class="space-y-4">
              <h1 class="text-5xl sm:text-6xl lg:text-7xl font-bold font-heading leading-tight animate-slide-in-left">
                <span class="bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 dark:from-slate-100 dark:via-blue-200 dark:to-indigo-200 bg-clip-text text-transparent drop-shadow-sm" set:html={hero.headline}></span>
              </h1>
              <h2 class="text-xl sm:text-2xl lg:text-3xl text-blue-600 dark:text-blue-400 font-semibold animate-slide-in-right animation-delay-200">
                {hero.subheadline}
              </h2>

              <p class="text-lg text-slate-600 dark:text-slate-300 leading-relaxed max-w-2xl animate-fade-in animation-delay-400">
                {hero.description}
              </p>
            </div>
          </div>
          
          <!-- Enhanced Tech Stack Pills -->
          <div class="flex flex-wrap gap-3 animate-fade-in-up animation-delay-600">
            {hero.highlights.map((highlight, index) => (
              <div class="flex items-center gap-3 px-5 py-3 bg-white/80 dark:bg-slate-800/80 backdrop-blur-md rounded-2xl border border-slate-200/50 dark:border-slate-600/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:bg-white dark:hover:bg-slate-700 group animate-bounce-in" style={`animation-delay: ${600 + index * 100}ms`}>
                <span class="text-xl group-hover:scale-110 transition-transform duration-300">{highlight.icon}</span>
                <span class="font-semibold text-slate-700 dark:text-slate-200 text-sm group-hover:text-blue-700 dark:group-hover:text-blue-400 transition-colors">{highlight.label}</span>
              </div>
            ))}
          </div>
          
          <!-- Enhanced CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 pt-4 animate-fade-in-up animation-delay-800">
            <a
              href={hero.primaryCTA.url}
              class="group inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl font-semibold shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300 relative overflow-hidden"
              aria-label="View my portfolio projects"
            >
              <div class="absolute inset-0 bg-gradient-to-r from-blue-700 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <span class="relative z-10">{hero.primaryCTA.text}</span>
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </a>
            <a
              href={hero.secondaryCTA.url}
              class="group inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-accent to-orange-500 text-white rounded-2xl font-bold shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300 relative overflow-hidden border-2 border-accent/50 hover:border-accent animate-pulse-subtle"
              aria-label="Get in touch with me"
            >
              <div class="absolute inset-0 bg-gradient-to-r from-orange-600 to-red-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <span class="relative z-10 font-bold">{hero.secondaryCTA.text}</span>
              <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </a>
          </div>
        </div>
        
        <!-- Visual Side -->
        <div class="relative">
          <div class="relative mx-auto w-80 h-80 lg:w-96 lg:h-96">
            <!-- Background decoration -->
            <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full blur-2xl opacity-20 animate-pulse"></div>
            <div class="absolute inset-4 bg-gradient-to-r from-orange-400 to-pink-400 rounded-full blur-xl opacity-30 animate-pulse delay-500"></div>
            
            <!-- Main image -->
            <div class="relative z-10 w-full h-full">
              <img
                src="/headshot.jpg"
                alt="Nob Hokleng - Enterprise Architect | Performance & Scalability"
                width="400"
                height="400"
                class="w-full h-full object-cover rounded-3xl shadow-2xl border-4 border-white/50 hover:scale-105 transition-transform duration-500"
                loading="eager"
              />
              
              <!-- Floating elements -->
              <div class="absolute -top-4 -right-4 w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg flex items-center justify-center animate-bounce">
                <span class="text-2xl">💻</span>
              </div>
              <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg flex items-center justify-center animate-bounce delay-300">
                <span class="text-2xl">⚡</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Scroll Indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <a href="#about" class="flex flex-col items-center text-slate-600 hover:text-blue-600 transition-colors group" aria-label="Scroll to about section">
          <span class="text-sm font-medium mb-2 group-hover:text-blue-600">Scroll to explore</span>
          <div class="w-6 h-10 border-2 border-slate-400 rounded-full flex justify-center group-hover:border-blue-600 transition-colors">
            <div class="w-1 h-3 bg-slate-400 rounded-full mt-2 animate-pulse group-hover:bg-blue-600"></div>
          </div>
        </a>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="about py-32 bg-gradient-to-br from-white to-slate-50" role="main">
    <div class="container mx-auto px-6 max-w-7xl">
      <div class="text-center mb-20">
        <h2 class="text-4xl md:text-5xl font-bold font-heading mb-6 bg-gradient-to-r from-slate-900 to-blue-900 bg-clip-text text-transparent">
          About Me
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto rounded-full"></div>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-20 items-start">
        <div class="space-y-8">
          <div class="prose prose-lg">
            <p class="text-xl font-medium text-slate-700 leading-relaxed mb-8">
              {about.openingLine}
            </p>
            {about.mainContent.map((paragraph) => (
              <p class="text-slate-600 leading-relaxed mb-6">
                {paragraph}
              </p>
            ))}
          </div>
          
          <div class="experience-highlights">
            <h3 class="text-2xl font-bold text-slate-800 mb-6 font-heading">Experience Highlights</h3>
            <div class="space-y-4">
              {about.experienceHighlights.map((highlight) => (
                <div class="flex items-start gap-4 p-4 bg-white/60 backdrop-blur-sm rounded-2xl border border-slate-200/50 hover:bg-white/80 transition-all duration-300">
                  <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mt-0.5">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <span class="text-slate-700 font-medium">{highlight}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div class="skills-section">
          <div class="bg-white/80 backdrop-blur-sm p-8 rounded-3xl shadow-xl border border-slate-200/50">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 text-center font-heading">Technical Skills</h3>
            <div class="grid grid-cols-1 gap-6">
              {about.skills.map((skillCategory) => (
                <div class="skill-category p-6 bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl border border-slate-200/30 hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <h4 class="text-lg font-semibold text-blue-700 mb-4 font-heading flex items-center gap-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    {skillCategory.category}
                  </h4>
                  <div class="flex flex-wrap gap-2">
                    {skillCategory.items.map((skill) => (
                      <span class="px-3 py-1 bg-white/70 text-slate-700 text-sm font-medium rounded-lg border border-slate-200/50 hover:bg-white hover:scale-105 transition-all duration-200">
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Portfolio Section -->
  <section id="portfolio" class="portfolio py-32 bg-gradient-to-br from-slate-100 to-blue-50 relative overflow-hidden" role="region" aria-labelledby="portfolio-title">
    <!-- Background decoration -->
    <div class="absolute inset-0">
      <div class="absolute top-1/3 left-1/3 w-64 h-64 bg-gradient-to-r from-blue-400/5 to-purple-400/5 rounded-full blur-3xl"></div>
      <div class="absolute bottom-1/3 right-1/3 w-80 h-80 bg-gradient-to-r from-orange-400/5 to-pink-400/5 rounded-full blur-3xl"></div>
    </div>
    
    <div class="container mx-auto px-6 max-w-7xl relative z-10">
      <div class="text-center mb-20">
        <h2 id="portfolio-title" class="text-4xl md:text-5xl font-bold font-heading mb-6 bg-gradient-to-r from-slate-900 to-blue-900 bg-clip-text text-transparent">
          Featured Projects
        </h2>
        <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-indigo-600 mx-auto rounded-full mb-6"></div>
        <p class="text-lg text-slate-600 max-w-2xl mx-auto">
          A showcase of my recent work in software development and system architecture
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" role="list">
        {featuredProjects.map((project, index) => (
          <a href={`/portfolio/${project.slug}`} class="block group">
            <ProjectCard
              title={project.data.title}
              description={project.data.problem}
              tags={project.data.technologies}
              slug={project.slug}
              heroImage={project.data.heroImage}
              index={index}
              projectType={project.data.projectType}
              featured={project.data.featured}
              status={project.data.status}
            />
          </a>
        ))}
      </div>
      
      <div class="text-center mt-16">
        <a href="/portfolio" class="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl font-semibold shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300">
          View All Projects
          <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</Layout>

<style>
  @keyframes pulse-subtle {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(255, 158, 0, 0.4);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(255, 158, 0, 0);
    }
  }

  .animate-pulse-subtle {
    animation: pulse-subtle 3s infinite;
  }
</style>