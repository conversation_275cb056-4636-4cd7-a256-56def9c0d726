---
import Layout from '../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import ProjectCard from '../../components/ProjectCard.astro';

const projects = await getCollection('portfolio');
---

<Layout title="Portfolio | Nob Hokleng | Software Developer & System Architect">
  <section class="portfolio pt-32 pb-24 bg-light" role="region" aria-labelledby="portfolio-title">
    <div class="container mx-auto px-5 max-w-6xl">
      <h1 id="portfolio-title" class="text-4xl font-bold text-center mb-12 font-heading relative">
        Portfolio
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h1>
      
      <!-- Portfolio Filters -->
      <div class="mb-12">
        <div class="flex flex-wrap gap-4 justify-center">
          <button class="filter-btn active px-4 py-2 bg-primary text-white rounded-lg font-medium hover:bg-primary/90 transition-colors" data-filter="all">
            All Projects
          </button>
          <button class="filter-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg font-medium hover:bg-gray-300 transition-colors" data-filter="web-app">
            Web Apps
          </button>
          <button class="filter-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg font-medium hover:bg-gray-300 transition-colors" data-filter="platform">
            Platforms
          </button>
          <button class="filter-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg font-medium hover:bg-gray-300 transition-colors" data-filter="system">
            Systems
          </button>
          <button class="filter-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg font-medium hover:bg-gray-300 transition-colors" data-filter="api">
            APIs
          </button>
        </div>

        <!-- Sort Options -->
        <div class="flex justify-center mt-4">
          <select id="sort-select" class="px-4 py-2 border border-gray-300 rounded-lg font-medium bg-white">
            <option value="date-desc">Newest First</option>
            <option value="date-asc">Oldest First</option>
            <option value="title-asc">Title A-Z</option>
            <option value="title-desc">Title Z-A</option>
          </select>
        </div>
      </div>

      <div id="portfolio-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" role="list">
        {projects.map((project, index) => (
          <div
            role="listitem"
            class="portfolio-item"
            data-type={project.data.projectType || 'web-app'}
            data-date={project.data.publishDate.getTime()}
            data-title={project.data.title}
            data-featured={project.data.featured || false}
          >
            <a href={`/portfolio/${project.slug}`} class="block">
              <ProjectCard
                title={project.data.title}
                description={project.data.problem}
                tags={project.data.technologies.slice(0, 3)}
                heroImage={project.data.heroImage}
                index={index}
                projectType={project.data.projectType}
                featured={project.data.featured}
                status={project.data.status}
              />
            </a>
          </div>
        ))}
      </div>
    </div>
  </section>
</Layout>

<script>
  // Portfolio filtering and sorting functionality
  document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const sortSelect = document.getElementById('sort-select') as HTMLSelectElement;
    const portfolioGrid = document.getElementById('portfolio-grid');
    const portfolioItems = document.querySelectorAll('.portfolio-item');

    let currentFilter = 'all';
    let currentSort = 'date-desc';

    // Filter functionality
    filterButtons.forEach(button => {
      button.addEventListener('click', () => {
        // Update active button
        filterButtons.forEach(btn => {
          btn.classList.remove('active', 'bg-primary', 'text-white');
          btn.classList.add('bg-gray-200', 'text-gray-700');
        });
        button.classList.add('active', 'bg-primary', 'text-white');
        button.classList.remove('bg-gray-200', 'text-gray-700');

        // Update filter
        currentFilter = button.getAttribute('data-filter') || 'all';
        applyFiltersAndSort();
      });
    });

    // Sort functionality
    sortSelect.addEventListener('change', () => {
      currentSort = sortSelect.value;
      applyFiltersAndSort();
    });

    function applyFiltersAndSort() {
      // Convert NodeList to Array for sorting
      const itemsArray = Array.from(portfolioItems);

      // Filter items
      const filteredItems = itemsArray.filter(item => {
        if (currentFilter === 'all') return true;
        return item.getAttribute('data-type') === currentFilter;
      });

      // Sort items
      filteredItems.sort((a, b) => {
        switch (currentSort) {
          case 'date-desc':
            return parseInt(b.getAttribute('data-date') || '0') - parseInt(a.getAttribute('data-date') || '0');
          case 'date-asc':
            return parseInt(a.getAttribute('data-date') || '0') - parseInt(b.getAttribute('data-date') || '0');
          case 'title-asc':
            return (a.getAttribute('data-title') || '').localeCompare(b.getAttribute('data-title') || '');
          case 'title-desc':
            return (b.getAttribute('data-title') || '').localeCompare(a.getAttribute('data-title') || '');
          default:
            return 0;
        }
      });

      // Hide all items first
      itemsArray.forEach(item => {
        item.style.display = 'none';
      });

      // Show and reorder filtered items
      filteredItems.forEach((item, index) => {
        item.style.display = 'block';
        item.style.order = index.toString();
      });

      // Update grid to use flexbox for ordering
      if (portfolioGrid) {
        portfolioGrid.style.display = 'flex';
        portfolioGrid.style.flexWrap = 'wrap';
        portfolioGrid.style.gap = '2rem';

        // Reset to grid if all items are shown
        if (filteredItems.length === itemsArray.length) {
          portfolioGrid.style.display = 'grid';
        }
      }
    }

    // Initialize with default filter and sort
    applyFiltersAndSort();
  });
</script>

<style>
  .portfolio-item {
    transition: opacity 0.3s ease, transform 0.3s ease;
  }

  .filter-btn {
    transition: all 0.3s ease;
  }

  .filter-btn.active {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  #portfolio-grid {
    min-height: 400px;
  }

  @media (max-width: 768px) {
    .portfolio-item {
      flex: 1 1 100%;
      max-width: 100%;
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    .portfolio-item {
      flex: 1 1 calc(50% - 1rem);
      max-width: calc(50% - 1rem);
    }
  }

  @media (min-width: 1025px) {
    .portfolio-item {
      flex: 1 1 calc(33.333% - 1.333rem);
      max-width: calc(33.333% - 1.333rem);
    }
  }
</style>